{"compilerOptions": {"baseUrl": ".", "target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "types": ["vitest/globals"], "noEmit": true, "paths": {"@/*": ["./src/*"], "@tests/*": ["./tests/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "references": [{"path": "./tsconfig.node.json"}], "exclude": ["node_modules"]}