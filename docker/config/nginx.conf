events {}

http {
    include /etc/nginx/mime.types;

    server {
        
        listen 8080;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;

             add_header Last-Modified $date_gmt;
             add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
             if_modified_since off;
             expires off;
             etag off;
        }
    }
}