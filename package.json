{"name": "@dfe/dfe-hello-world-frontend", "version": "1.0.9-alpha.16", "private": false, "type": "module", "engines": {"node": "^18.17.1"}, "files": ["dist/*"], "publishConfig": {"@dfe:registry": "https://gitlab.dachser.com/api/v4/projects/${CI_PROJECT_ID}/packages/npm/"}, "scripts": {"dev": "vite", "preview": "npm run build && vite preview --port 3005", "build": "vue-tsc --noEmit && vite build", "build-dev": "npm run build -- --mode development", "pretest:unit": "node ./tests/helper/prepareTests.cjs", "test:watch": "npm run pretest:unit && vitest", "test:unit": "vitest run", "test": "npm run test:unit -- --coverage", "lint": "eslint .", "generate-translations": "del-cli --force src/locales/*.json && npx @dfe/dfe-frontend-i18n src/locales 170", "generate-translations-dev": "npx @dfe/dfe-frontend-i18n src/locales 170"}, "dependencies": {"@dfe/dfe-frontend-client": "^0.26.0", "@dfe/dfe-frontend-styles": "^0.11.0", "@dfe/dfe-hello-world-api-module": "^1.0.0-alpha.2", "@tanstack/vue-query": "^5.28.1", "lodash": "^4.17.21", "pinia": "^2.1.6", "vue": "^3.4.21", "vue-i18n": "^9.10.1", "vuetify": "^3.5.9"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^3.0.1", "@originjs/vite-plugin-federation": "^1.3.5", "@pinia/testing": "^0.1.3", "@types/lodash": "^4.17.0", "@vitejs/plugin-vue": "^5.0.4", "@vitest/coverage-v8": "^1.3.1", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.5", "autoprefixer": "^10.4.18", "del-cli": "^5.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-vue": "^9.23.0", "jsdom": "^24.0.0", "postcss-prefix-selector": "^1.16.0", "resize-observer-polyfill": "^1.5.1", "sass": "^1.72.0", "typescript": "^5.4.2", "vite": "^5.1.6", "vite-plugin-css-injected-by-js": "^3.4.0", "vite-plugin-vuetify": "^2.0.3", "vite-svg-loader": "^5.1.0", "vitest": "^1.3.1", "vitest-fetch-mock": "^0.2.2", "vue-tsc": "^2.0.6", "wait-for-expect": "^3.0.2"}}