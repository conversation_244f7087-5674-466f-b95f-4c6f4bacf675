import { URL, fileURLToPath } from "node:url";
import { mergeConfig } from "vite";
import { configDefaults, defineConfig } from "vitest/config";

import viteConfig from "./vite.config";

export default mergeConfig(
  viteConfig({ command: "serve", mode: "test" }),
  defineConfig({
    resolve: {
      alias: {
        "@tests": fileURLToPath(new URL("./tests", import.meta.url)),
      },
    },
    test: {
      root: fileURLToPath(new URL(".", import.meta.url)),
      setupFiles: "./tests/helper/setupTests.ts",
      environment: "jsdom",
      include: ["**/tests/**/*.spec.[jt]s?(x)"],
      exclude: [...configDefaults.exclude],
      globals: true,
      server: {
        deps: {
          inline: ["vuetify"],
        },
      },
      coverage: {
        provider: "v8",
        reporter: ["text", "lcov"],
        include: ["src/**/*.{ts,js,vue}"],
        exclude: ["src/dev/**/*", "src/types/**/*", "src/**/generated*", "src/mocks/**/*"],
      },
    },
  })
);
