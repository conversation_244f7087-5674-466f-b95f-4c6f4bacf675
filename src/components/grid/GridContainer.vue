<script setup lang="ts"></script>

<template>
  <v-container fluid class="grid-container">
    <slot />
  </v-container>
</template>

<style lang="scss" scoped>
@use "@dfe/dfe-frontend-styles/build/scss/variables" as vars;
@import "@/styles/settings";

.grid-container {
  @media #{map-get($display-breakpoints, 'sm-and-up')} {
    padding: vars.$spacing-grid-container-sm;
  }
  @media #{map-get($display-breakpoints, 'xl')} {
    padding: vars.$spacing-grid-container-xl;
  }
}
</style>
