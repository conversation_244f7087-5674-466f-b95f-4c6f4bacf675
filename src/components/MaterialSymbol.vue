<script setup lang="ts">
import { computed } from "vue";

interface Props {
  size?: number | string;
  color?: string;
  backgroundColor?: string;
  rounded?: boolean;
  left?: boolean;
  right?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  size: 24,
});

const className = computed(() => [
  ...(props?.color ? [`text-${props.color}`] : []),
  ...(props?.backgroundColor ? [`bg-${props.backgroundColor}`, "has-background"] : []),
  ...(props?.rounded ? ["rounded-circle"] : []),
  ...(props?.left ? ["left"] : []),
  ...(props?.right ? ["right"] : []),
]);
</script>

<template>
  <span class="material-symbol" :class="className" :style="{ fontSize: `${props.size}px` }">
    <slot />
  </span>
</template>

<style lang="scss" scoped>
.material-symbol {
  display: inline-flex;

  &.has-background {
    padding: 6px;
  }

  :deep(.icon) {
    fill: currentColor;
    height: 1em;
    width: 1em;
  }
}
</style>
