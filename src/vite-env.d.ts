/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_DFE_HELLO_WORLD_API_URL: string;
  readonly VITE_APP_DFE_LOGGING_API_URL: string;
  readonly VITE_APP_DFE_ENVIRONMENT: string;
  readonly VITE_APP_KEYCLOAK_URL: string;
  readonly VITE_APP_KEYCLOAK_REALM: string;
  readonly VITE_APP_KEYCLOAK_CLIENT_ID: string;
  readonly VITE_APP_I18N_MISSING_WARN: string;
  readonly VITE_APP_I18N_FALLBACK_WARN: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
