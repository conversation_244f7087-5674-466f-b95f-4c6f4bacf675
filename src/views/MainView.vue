<script setup lang="ts">
import { ref, inject } from "vue";
import GridContainer from "@/components/grid/GridContainer.vue";
import StarIcon from "@dfe/dfe-frontend-styles/assets/icons/star-24px.svg";
import AddIcon from "@dfe/dfe-frontend-styles/assets/icons/add-24px.svg";
import MaterialSymbol from "@/components/MaterialSymbol.vue";
import { useQuery } from "@tanstack/vue-query";
import { ApiKey } from "@/services/api";

const api = inject(ApiKey);

const page = ref(1);
const size = ref(10);

const counter = ref(0);

const onHelloWorld = () => {
  counter.value += 1;
};
const { data, isLoading } = useQuery({
  queryKey: ["customers", page, size],

  queryFn: async () => api?.helloWorld.customers.getCustomers({}).then((response) => response.data),
  refetchOnWindowFocus: false,
  staleTime: 0,
  enabled: true,
  retry: 2,
});
</script>

<template>
  <div class="main-container bg-grey-lighten-3 h-100 w-100 overflow-hidden">
    <div class="w-100 d-flex h-100 overlow-hidden flex-column">
      <grid-container class="pb-0">
        <h2 class="text-h2 text-grey-900 mb-4">{{ $t("labels.main.text") }}</h2>
        <v-row>
          <v-col class="d-flex flex-grow-0 align-end">
            <material-symbol left size="24">
              <star-icon />
            </material-symbol>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <div>{{ isLoading }}</div>
            <div>{{ data }}</div>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-btn
              variant="outlined"
              color="primary"
              data-test="hello-world-button"
              @click="onHelloWorld"
            >
              <material-symbol left size="24"> <add-icon /> {{ counter }} </material-symbol>
            </v-btn>
          </v-col>
        </v-row>
      </grid-container>
    </div>
  </div>
</template>
