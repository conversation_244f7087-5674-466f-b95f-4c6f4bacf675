import type { InjectionKey } from "vue";
import {
  Api as HelloWorldApi,
  HttpResponse as AddressHttpResponse,
  FullRequestParams as AddressFullRequestParams,
} from "@dfe/dfe-hello-world-api-module";

export type ApiSecurityDataFn = () => Promise<string | undefined>;

export type Api = {
  helloWorld: HelloWorldApi<ApiSecurityDataFn>;
};

export const ApiKey: InjectionKey<Api> = Symbol("Api");

interface ApiConfig {
  baseUrl?: string;
  getToken: ApiSecurityDataFn;
  getLanguage: () => string;
}

const Api = {
  HelloWorld: HelloWorldApi,
};

export function createApi(
  Api: "HelloWorld",
  { baseUrl, getToken, getLanguage }: ApiConfig
): HelloWorldApi<ApiSecurityDataFn>;

export function createApi(Key: keyof typeof Api, { baseUrl, getToken, getLanguage }: ApiConfig) {
  const api = new Api[Key]({
    baseUrl,
    securityWorker: async (securityData: ApiSecurityDataFn | null) => {
      return securityData ? { headers: { Authorization: `Bearer ${await securityData()}` } } : {};
    },
  });

  const request = api.request;

  type FullRequestParams = AddressFullRequestParams;
  type HttpResponse<T, E> = AddressHttpResponse<T, E>;

  api.request = async <T = any, E = any>(
    params: FullRequestParams
  ): Promise<HttpResponse<T, E>> => {
    return request<T, E>({
      ...params,
      headers: {
        "Accept-Language": getLanguage(),
        ...params.headers,
      },
    });
  };

  api.setSecurityData(getToken);

  return api;
}
