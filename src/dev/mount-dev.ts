import { DFEClient, createClient } from "@dfe/dfe-frontend-client";
import { createApp } from "vue";
import DevHeader from "@/dev/DevHeader.vue";
import { registerPlugins } from "@/plugins";
import { ClientKey } from "@/types/client";
import { Events } from "@/types/events";

type App = {
  el: Element | null;
  mount: (el: Element, client: DFEClient<Events>) => void;
};

export function mountDev(apps: App[]) {
  if (import.meta.env.VITE_APP_DFE_ENVIRONMENT === "development") {
    const client = createClient<Events>({
      api: {
        "dfe-hello-world-frontend": import.meta.env.VITE_APP_DFE_HELLO_WORLD_API_URL,
      },
      logger: {
        baseUrl: import.meta.env.VITE_APP_DFE_LOGGING_API_URL,
      },
      environment: import.meta.env.VITE_APP_DFE_ENVIRONMENT,
    });

    client.auth.init({
      url: import.meta.env.VITE_APP_KEYCLOAK_URL,
      realm: import.meta.env.VITE_APP_KEYCLOAK_REALM,
      clientId: import.meta.env.VITE_APP_KEYCLOAK_CLIENT_ID,
      onAuth() {
        apps.forEach(({ el, mount }) => {
          if (el) {
            mount(el, client);
          }
        });

        const headerApp = createApp(DevHeader);

        headerApp.provide(ClientKey, client);
        registerPlugins(headerApp);
        headerApp.mount("#__dfe-hello-world-frontend-dev-header-root");
      },
    });
  }
}
