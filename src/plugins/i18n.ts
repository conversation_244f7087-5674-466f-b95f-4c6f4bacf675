import { createI18n } from "vue-i18n";
import en from "@/locales/en.json";

type Locale = "en";
type MessageSchema = typeof en;
export type UseI18nOptions = {
  message: MessageSchema;
};

const fallbackLocale = "en";
const loadedLocales = [fallbackLocale];

export const i18n = createI18n<[MessageSchema], Locale, false>({
  locale: fallbackLocale,
  fallbackLocale,
  legacy: false,
  messages: {
    en,
  },
});

i18n.global.missingWarn = import.meta.env.VITE_APP_I18N_MISSING_WARN === "true";
i18n.global.fallbackWarn = import.meta.env.VITE_APP_I18N_FALLBACK_WARN === "true";

export function setI18nLanguage(instance: typeof i18n, locale: string) {
  instance.global.locale.value = locale as Locale;
}

export const useLocale = async (locale: string): Promise<void> => {
  if (i18n.global.locale.value === locale) {
    return;
  }

  if (loadedLocales.includes(locale)) {
    setI18nLanguage(i18n, locale);
    return;
  }

  try {
    const messages = await import(`@/locales/${locale}.json`);
    i18n.global.setLocaleMessage(locale, messages);
    setI18nLanguage(i18n, locale);
    loadedLocales.push(locale);
  } catch (error) {
    // Ignore new locale
  }
};
