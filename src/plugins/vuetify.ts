import "vuetify/styles";
import { createVuetify } from "vuetify";
import ChevronLeftIcon from "@dfe/dfe-frontend-styles/assets/icons/chevron_left-24px.svg";
import ChevronRightIcon from "@dfe/dfe-frontend-styles/assets/icons/chevron_right-24px.svg";
import ArrowUpIcon from "@dfe/dfe-frontend-styles/assets/icons/arrow_up-16px.svg";
import ArrowDownIcon from "@dfe/dfe-frontend-styles/assets/icons/arrow_down-16px.svg";
import ArrowDropDownIcon from "@dfe/dfe-frontend-styles/assets/icons/arrow_drop_down-24px.svg";
import { ThemeVuetify3 } from "@dfe/dfe-frontend-styles/build/ts/variables";
import { VBtn } from "vuetify/components";

export default createVuetify({
  theme: ThemeVuetify3,
  aliases: {
    VIconBtn: VBtn,
  },
  defaults: {
    global: {
      ripple: false,
    },
    VBtn: {
      variant: "flat",
      rounded: "xs",
    },
    VIconBtn: {
      variant: "plain",
      class: "v-icon-btn",
    },
    VSelect: {
      variant: "outlined",
      density: "compact",
    },
    VTextField: {
      "single-line": true,
      variant: "outlined",
      density: "compact",
    },
    VAutocomplete: {
      "single-line": true,
      variant: "outlined",
      density: "compact",
    },
    VCardActions: {
      VBtn: {
        variant: "flat",
      },
    },
  },
  icons: {
    aliases: {
      sortAsc: ArrowDownIcon,
      sortDesc: ArrowUpIcon,
      prev: ChevronLeftIcon,
      next: ChevronRightIcon,
      dropdown: ArrowDropDownIcon,
      selectMenu: ArrowDropDownIcon,
      menuSelect: ArrowDropDownIcon,
    },
  },
});
