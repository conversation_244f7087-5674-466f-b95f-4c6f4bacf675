import type { DFEClient, Preferences } from "@dfe/dfe-frontend-client";
import { createApp, ref, type Component } from "vue";
import { registerPlugins } from "@/plugins";
import { i18n, useLocale } from "@/plugins/i18n";
import pinia from "@/plugins/pinia";
import { ApiKey, createApi, type Api } from "@/services/api";
import { ClientKey } from "@/types/client";
import type { Events } from "@/types/events";

export function mountApp(rootComponent: Component) {
  return async (element: Element, client: DFEClient<Events>) => {
    const { locale, dateFormat, timeFormat, dateFormatPlaceholder } = client.preferences;
    const preferences = ref<Preferences>({
      locale,
      dateFormat,
      timeFormat,
      dateFormatPlaceholder,
    });
    await useLocale(locale);

    const removePreferencesListener = client.preferences.onChange((values) => {
      preferences.value = values;
      useLocale(values.locale);
    });

    const api: Api = {
      helloWorld: createApi("HelloWorld", {
        baseUrl: client.api.getUrl("dfe-hellow-world"),
        getToken: client.auth.getToken,
        getLanguage: () => i18n.global.locale.value,
      }),
    };
    pinia.use(() => ({
      api,
      client,
    }));

    const app = createApp(rootComponent);

    registerPlugins(app);

    app.provide(ClientKey, client);
    app.provide(ApiKey, api);
    app.mount(element);

    return () => {
      removePreferencesListener();
      client.events.emit("exampleEvent");
      app.unmount();
    };
  };
}
