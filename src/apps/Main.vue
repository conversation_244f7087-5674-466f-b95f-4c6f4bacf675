<script setup lang="ts">
import MainView from "@/views/MainView.vue";
</script>

<template>
  <div dfe-hello-world-frontend class="h-100 overflow-auto">
    <div class="v-application v-application-hello-world v-application--is-ltr h-100">
      <v-theme-provider theme="light">
        <slot>
          <main-view />
        </slot>
      </v-theme-provider>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import "vuetify/lib/styles/settings/variables";
[dfe-hello-world-frontend] {
  @import "@/styles/overrides";
}
</style>
