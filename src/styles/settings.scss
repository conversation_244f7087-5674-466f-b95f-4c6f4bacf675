@use "@dfe/dfe-frontend-styles/build/scss/variables" as vars;

@forward "vuetify/settings" with (
  $color-pack: false,

  $button-border-radius: vars.$button-border-radius,
  $button-padding-ratio: 3,
  $button-content-transition: vars.$button-animation-transition,
  $button-transition-property: box-shadow,
  $button-text-transform: vars.$button-text-transform,
  $button-text-letter-spacing: vars.$button-text-letter-spacing,
  $button-height: vars.$button-size-base,
  $button-font-size: vars.$button-text-size-base,

  $btn-toggle-selected-opacity: vars.$button-toggle-opacity,

  $table-background: rgb(var(--v-theme-grey-200)),
  $table-column-padding: 16px 16px 16px 0,
  $table-header-font-weight: vars.$font-weight-label,
  $table-line-height: vars.$line-height-body-2,
  $table-border: none,
  $table-row-height: 90px,

  $divider-opacity: 1,
  $divider-border-color: rgb(var(--v-theme-grey-400)),
  $grid-gutter: vars.$spacing-grid-gutter-base,
  $grid-gutters: (
    "xs": vars.$spacing-grid-gutter-xs,
    "sm": vars.$spacing-grid-gutter-sm,
    "md": vars.$spacing-grid-gutter-md,
    "lg": vars.$spacing-grid-gutter-lg,
    "xl": vars.$spacing-grid-gutter-xl,
  ),
  $container-padding-x: vars.$spacing-grid-container-base,

  $overlay-scrim-background: rgb(var(--v-theme-black)),
  $overlay-opacity: 0.75,

  $dialog-margin: 0px,
  $dialog-card-text-padding: 24px,
  $dialog-border-radius: 8px,

  $card-background: rgb(var(--v-theme-white)),
  $card-border-radius: 8px,
  $card-title-padding: 24px 24px 16px,
  $card-actions-padding: 16px 24px 24px,
  $button-card-actions-padding: 0px 12px,
  $button-card-actions-margin: 12px,

  $field-font-size: vars.$font-size-body-2,
  $field-letter-spacing: 1,
  $field-focused-border-width: 1px,
  $field-input-min-height: vars.$form-input-height,
  $field-control-padding-start: 12px,
  $field-control-padding-end: 12px,
  $field-control-padding-top: 8px,
  $field-control-padding-bottom: 8px,
  $input-chips-margin-top: 0px,
  $input-chips-margin-bottom: (
    "default": 0,
    "comfortable": 0,
    "compact": 0,
  ),
  $input-details-padding-above: 4px,
  $input-details-min-height: 0,

  $body-font-family: vars.$font-family-base,
  $heading-font-family: vars.$font-family-base,
  $font-size-root: vars.$font-size-base,
  $typography: (
    "h1": (
      "size": vars.$font-size-heading-1,
      "weight": vars.$font-weight-heading,
      "line-height": vars.$line-height-heading-1,
      "letter-spacing": vars.$letter-spacing-heading-1,
      "font-family": vars.$font-family-base,
    ),
    "h2": (
      "size": vars.$font-size-heading-2,
      "weight": vars.$font-weight-heading,
      "line-height": vars.$line-height-heading-2,
      "letter-spacing": vars.$letter-spacing-heading-2,
      "font-family": vars.$font-family-base,
    ),
    "h3": (
      "size": vars.$font-size-heading-3,
      "weight": vars.$font-weight-heading,
      "line-height": vars.$line-height-heading-3,
      "font-family": vars.$font-family-base,
    ),
    "h4": (
      "size": vars.$font-size-heading-4,
      "weight": vars.$font-weight-heading,
      "line-height": vars.$line-height-heading-4,
      "font-family": vars.$font-family-base,
    ),
    "h5": (
      "size": vars.$font-size-heading-5,
      "weight": vars.$font-weight-heading,
      "line-height": vars.$line-height-heading-5,
      "font-family": vars.$font-family-base,
    ),
    "h6": (
      "size": vars.$font-size-heading-6,
      "weight": vars.$font-weight-heading,
      "line-height": vars.$line-height-heading-6,
      "font-family": vars.$font-family-base,
    ),
    "body-1": (
      "size": vars.$font-size-body-1,
      "weight": vars.$font-weight-body,
      "line-height": vars.$line-height-body-1,
      "font-family": vars.$font-family-base,
    ),
    "body-2": (
      "size": vars.$font-size-body-2,
      "weight": vars.$font-weight-body,
      "line-height": vars.$line-height-body-2,
      "font-family": vars.$font-family-base,
    ),
    "body-3": (
      "size": vars.$font-size-body-3,
      "weight": vars.$font-weight-body,
      "line-height": vars.$line-height-body-3,
      "letter-spacing": normal,
      "font-family": vars.$font-family-base,
      "text-transform": none,
    ),
    "caption": (
      "size": vars.$font-size-caption,
      "weight": vars.$font-weight-body,
      "line-height": vars.$line-height-caption,
      "font-family": vars.$font-family-base,
    ),
    "label-1": (
      "size": vars.$font-size-label-1,
      "weight": vars.$font-weight-label,
      "line-height": vars.$line-height-label-1,
      "letter-spacing": normal,
      "font-family": vars.$font-family-base,
      "text-transform": none,
    ),
    "label-2": (
      "size": vars.$font-size-label-2,
      "weight": vars.$font-weight-label,
      "line-height": vars.$line-height-label-2,
      "letter-spacing": normal,
      "font-family": vars.$font-family-base,
      "text-transform": none,
    ),
    "label-3": (
      "size": vars.$font-size-label-3,
      "weight": vars.$font-weight-label,
      "line-height": vars.$line-height-label-3,
      "letter-spacing": normal,
      "font-family": vars.$font-family-base,
      "text-transform": none,
    ),
  ),

  $list-item-title-font-size: vars.$font-size-body-2,
  $list-item-title-line-height: vars.$line-height-body-2,
  $list-item-title-letter-spacing: normal,
  $list-padding: 0,
  $list-item-padding: 0 12px,
  $list-item-one-line-min-height: 36px,

  $menu-content-border-radius: 0
);
