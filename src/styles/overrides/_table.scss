@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
@import "@/styles/settings";
:deep() {
  .v-data-table {
    display: flex !important;
    flex-direction: column !important;
    height: 100%;
    overflow: hidden;
    position: relative;
    background-color: rgb(var(--v-theme-white)) !important;
    .v-table__wrapper {
      background-color: rgb(var(--v-theme-grey-200)) !important;
      padding-top: 0;
      @media #{map-get($display-breakpoints, 'sm-and-up')} {
        padding: 0 vars.$spacing-grid-container-sm;
      }
      @media #{map-get($display-breakpoints, 'xl')} {
        padding: 0 vars.$spacing-grid-container-xl;
      }
      table {
        border-collapse: separate !important;
        border-spacing: 0 8px !important;
        width: 100%;
        tbody tr:hover {
          box-shadow: vars.$box-shadow-xs !important;
        }
        td {
          overflow: hidden;
          font-size: vars.$font-size-body-2;
          font-weight: vars.$font-weight-body;
          background-color: rgb(var(--v-theme-white));
          &:last-child {
            border-radius: 0 10px 10px 0 !important;
          }
          &:first-child {
            border-radius: 10px 0 0 10px !important;
          }
        }
        th {
          background-color: rgb(var(--v-theme-grey-200)) !important;
          color: rgb(var(--v-theme-grey-700));
          box-shadow: unset !important;
          padding: 24px 0 !important;
          height: auto !important;
          font-size: vars.$font-size-label-2;
          line-height: vars.$line-height-label-2;
          border-bottom: 1px solid rgb(var(--v-theme-grey-400)) !important;
        }
        
      }
    }
    .v-data-table-footer {
      font-size: vars.$font-size-body-2;
      font-weight: vars.$font-weight-body;
      line-height: vars.$line-height-body-2;
      color: rgb(var(--v-theme-grey-900)) !important;
      background-color: rgb(var(--v-theme-white));
      padding: 8px !important;
      border-top: 1px solid rgb(var(--v-theme-grey-400));
      button[aria-label='Last page'], button[aria-label='First page'] {
        display: none;
      }
      .v-data-table-footer__info {
        padding-inline-end: 16px !important;
      }
      .v-data-table-footer__items-per-page {
        padding-inline-end: 32px !important;
        & > span {
          padding-inline-end: 16px !important;
          color: rgb(var(--v-theme-grey-700)) !important;
        }
      }
      .v-btn--icon.v-btn--density-default {
        height: 24px !important;
        width: 24px !important;
        margin-right: 16px !important;
        &:not(.v-btn--disabled) {
          & svg path {
            fill: rgb(var(--v-theme-grey-900));
          }
          
          &:hover path{
            fill: rgb(var(--v-theme-blue-500));
          }
        }
        &.v-btn--disabled path {
          fill: rgb(var(--v-theme-grey-500)) !important;
        }
      }
      .v-input {
        .v-field__append-inner svg path{
          fill: rgb(var(--v-theme-grey-700)) !important;
        }
        .v-select__selection-text {
          font-size: vars.$font-size-body-2;
          font-weight: vars.$font-weight-body;
          line-height: vars.$line-height-body-2;
          color: rgb(var(--v-theme-grey-900)) !important;
        }
        .v-field__input {
          min-height: 36px !important;
        }
      }
    }
  }
}
