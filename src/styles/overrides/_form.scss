@use "@/styles/settings";
@use "@dfe/dfe-frontend-styles/build/scss/variables" as vars;

:deep(.v-form) {
  .v-field__input {
    line-height: map-get($typography, "body-2", "line-height");
  }

  .v-input {
    .v-field__outline {
      --v-field-border-opacity: 1;
      color: vars.$form-border-color-default;
    }

    &:hover {
      .v-field__outline {
        color: vars.$form-border-color-hover;
      }
    }

    &.v-field--focused {
      .v-field__outline {
        color: vars.$form-border-color-focused;
      }
    }

    &.v-input--error {
      .v-field__outline {
        color: vars.$form-border-color-error;
      }
    }
  }

  .v-input__details {
    color: rgb(var(--v-theme-grey-700));
    letter-spacing: normal;
    padding-left: 0;
    padding-right: 0;

    .v-messages__message {
      transition: none !important;
    }
  }
}

:deep(.v-overlay) {
  .v-autocomplete__content {
    box-shadow: vars.$box-shadow-m;
  }
}
