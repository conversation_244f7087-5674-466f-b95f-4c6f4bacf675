@use "@/styles/settings";

:deep(.v-btn) {
  &.bg-primary {
    &:hover {
      background-color: rgb(var(--v-theme-blue-700)) !important;
    }

    &:focus-visible {
      box-shadow: inset 0 0 0 2px rgb(var(--v-theme-blue-700));
    }
  }

  &.text-primary {
    &:hover {
      background-color: rgb(var(--v-theme-blue-100)) !important;
    }

    &:focus-visible {
      box-shadow: inset 0 0 0 1px rgb(var(--v-theme-blue-700));
      color: rgb(var(--v-theme-blue-700)) !important;
    }
  }

  &::after {
    content: none;
  }

  &:focus-visible {
    // Safari doesn't support outline-offset with border radius. Therefore, we use box-shadow inset instead.
    box-shadow: inset 0 0 0 2px rgb(var(--v-theme-blue-700));
  }

  &.v-btn--size-small {
    font-size: settings.$button-font-size;
  }

  &.v-icon-btn {
    height: auto;
    opacity: 1;
    min-width: 0;
    padding: 0;
    border-radius: 50%;
    color: rgb(var(--v-theme-grey-700));

    &:hover {
      color: rgb(var(--v-theme-grey-900));
    }
  }

  .v-btn__overlay {
    display: none;
  }

  .material-symbol {
    &.left {
      margin-left: -4px;
      margin-right: 8px;
    }

    &.right {
      margin-left: 4px;
      margin-right: -4px;
    }
  }
}
