include:
  - project: 'dachser/business-integration/dachser-platform/devops/gitlab-components'
    file: '/ci-npm-docker.gitlab-ci.yml'
    ref: 'feat/gitlabRegistry'

# Publish npm package to GitLab Package Registry
publish-npm:
  stage: deploy
  image: node:18
  script:
    - echo "@dfe:registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/" > .npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}" >> .npmrc
    - npm publish
  rules:
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

#tes4544
# include:
#   - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/build-info@main
#   - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-info@main
#   - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-build@feat/npmRegistry
#     inputs:
#       build-number: $BUILD_NUMBER
#       build-name: $BUILD_NAME
#   - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-test@main
#   - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/sonar-scan@main
#     inputs:
#       sonar-project-name: $NPM_NAME
#       sonar-project-version: $NPM_VERSION
#   - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/normalize-image@main
#     inputs:
#       image-tag: $NPM_VERSION
#   - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/docker@main
#     inputs:
#       image_name: $DOCKER_IMAGE
#       image_tag: $DOCKER_TAG
#   - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/publish-charts@main
#     inputs:
#       application-version: $NPM_VERSION
#       docker-digest: $IMAGE_DIGEST
#   - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/semantic-release@main
#   - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/create-mr@main

# stages:
#   - prepare
#   - build
#   - test
#   - post-test
#   - build-images
#   - deploy
#   - release

# default:
#   tags:
#     - bi-dp-docker-01


# # Specifies, if a pipeline is created at all. Current configuration runs pipelines for tags, merge requests and
# # for branches that are not associated with a merge request.
# workflow:
#   rules:
#     - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
#       when: never
#     # After release pipeline is finished, the next commit on the same branch will not trigger a new pipeline
#     - if: $CI_COMMIT_BRANCH && $CI_COMMIT_MESSAGE =~ /^release:.*/
#       when: never
#     - if: $CI_COMMIT_TAG && $CI_COMMIT_TAG =~ $ALPHA_PATTERN
#       variables:
#         ARTIFACTORY_REPO_DEPLOY_RELEASES: $ARTIFACTORY_REPO_DEPLOY_RELEASES_DEV
#         ARTIFACTORY_REPO_DEPLOY_SNAPSHOTS: $ARTIFACTORY_REPO_DEPLOY_RELEASES_DEV
#         ARTIFACTORY_REPO_DEPLOY_NPM: $ARTIFACTORY_REPO_DEPLOY_NPM_DEV
#     - if: $CI_COMMIT_TAG && $CI_COMMIT_TAG =~ $BETA_PATTERN
#       variables:
#         ARTIFACTORY_REPO_DEPLOY_RELEASES: $ARTIFACTORY_REPO_DEPLOY_RELEASES_BETA
#         ARTIFACTORY_REPO_DEPLOY_SNAPSHOTS: $ARTIFACTORY_REPO_DEPLOY_RELEASES_BETA
#         ARTIFACTORY_REPO_DEPLOY_NPM: $ARTIFACTORY_REPO_DEPLOY_NPM_BETA
#     - if: $CI_COMMIT_TAG && $CI_COMMIT_TAG !~ $BETA_PATTERN && $CI_COMMIT_TAG !~ $ALPHA_PATTERN
#       variables:
#         ARTIFACTORY_REPO_DEPLOY_RELEASES: $ARTIFACTORY_REPO_DEPLOY_RELEASES_MASTER
#         ARTIFACTORY_REPO_DEPLOY_SNAPSHOTS: $ARTIFACTORY_REPO_DEPLOY_RELEASES_MASTER
#         ARTIFACTORY_REPO_DEPLOY_NPM: $ARTIFACTORY_REPO_DEPLOY_NPM_MASTER
#     - if: $CI_PIPELINE_SOURCE == "merge_request_event"
#     - if: $CI_COMMIT_BRANCH
#     - if: $CI_COMMIT_TAG

# variables:
#   # Branches
#   MASTER_BRANCH: "master"
#   BETA_BRANCH: "develop"
#   ALPHA_BRANCH: "alpha"
#   BETA_PATTERN: '/rc/'
#   ALPHA_PATTERN: '/alpha/'
#   BETA_RELEASES_ACTIVE: "true"

#   # Base images
#   BUILDER_SSH: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/ssh:latest
#   NODE_SLIM: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/nodejs:latest

#   # Artifactory
#   JFROG_PROJECT: bint
#   ARTIFACTORY_REPO_RESOLVE_RELEASES: bint-maven-dfe-all
#   ARTIFACTORY_REPO_RESOLVE_SNAPSHOTS: bint-maven-dfe-all
#   ARTIFACTORY_REPO_RESOLVE_NPM: npm-all

#   ARTIFACTORY_REPO_DEPLOY_NPM_DEV: bint-npm-dfe
#   ARTIFACTORY_REPO_DEPLOY_NPM_BETA: bint-npm-dfe
#   ARTIFACTORY_REPO_DEPLOY_NPM_MASTER: bint-npm-dfe

#   # These variables are set in workflow for three main branches, these are defatuls for feature branches and MRs
#   ARTIFACTORY_REPO_DEPLOY_RELEASES: $ARTIFACTORY_REPO_DEPLOY_RELEASES_DEV
#   ARTIFACTORY_REPO_DEPLOY_SNAPSHOTS: $ARTIFACTORY_REPO_DEPLOY_RELEASES_DEV
#   ARTIFACTORY_REPO_DEPLOY_NPM: $ARTIFACTORY_REPO_DEPLOY_NPM_DEV

#   # Docker
#   BASE_DOCKER_REPO: "docker-artifactory.dach041.dachser.com"
#   IMAGE_TARGET_REPO: "bint-docker-dfe"
#   IMAGE_GROUP: "dfe"

#   NPM_RUN_TESTS: "true"
