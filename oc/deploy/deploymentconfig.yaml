apiVersion: apps.openshift.io/v1
kind: DeploymentConfig
metadata:
  name: VAR_APP_NAME
  labels:
    gitHash: "VAR_GIT_HASH"
spec:
  replicas: 1  
  selector:    
    name: VAR_APP_NAME
  template:    
    metadata:
      labels:
        name: VAR_APP_NAME
        app: VAR_APP_NAME
        gitHash: "VAR_GIT_HASH"
    spec:
      containers:
      - image: VAR_CONTAINER_IMAGE
        name: VAR_APP_NAME
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          protocol: TCP
        resources:
          limits:
            cpu: 200m
            ephemeral-storage: 100Mi
            memory: 150Mi
          requests:
            cpu: 10m
            ephemeral-storage: 10Mi
            memory: 10Mi
      restartPolicy: Always
  triggers:
    - type: ConfigChange
  strategy:
    type: Rolling