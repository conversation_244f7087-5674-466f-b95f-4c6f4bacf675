apiVersion: tekton.dev/v1beta1
kind: PipelineRun
metadata:
  generateName: build-dfe-hello-world-frontend-
spec:
  pipelineRef:
    name: build-docker-deploy
  params:
    - name: git-url
      value: >-
        https://bitbucket.dach041.dachser.com/scm/dfe/dfe-hello-world-frontend.git
    - name: git-revision
      value: develop
    - name: pr-id
      value: ""
    - name: manifest-base-directory
      value: oc/deploy
    - name: project-key
      value: DFE
    - name: git-hash
      value: ""
    - name: app-name
      value: dfe-hello-world-frontend
    - name: image-target-repo
      value: bint-docker-dfe
    - name: artifactory-access-token-secret
      value: artifactory-token
    - name: notify-mail-recipients
      value: <EMAIL>
  workspaces:
    - name: project
      persistentVolumeClaim:
        claimName: pvc-docker-project-workspace
    - name: jf-home
      persistentVolumeClaim:
        claimName: pvc-jf-home-workspace
    - name: git-basic-auth
      secret:
        secretName: bitbucket-basic-auth
    - name: argocd-template-dir
      persistentVolumeClaim:
        claimName: pvc-argocd-templates-workspace
