# dfe-hello-world-frontend

## Features

- Vue 3
- Vuetify 3
- Typescript
- Vitest
- ESLint
- Prettier
- Vite Module Federation (Remote)

The project is built with Vue 3 and Typescript. Vuetify 3 is used as the UI library.

## Project setup

```
npm install
```

## Available scripts

- `serve`: Serves in development mode
- `preview`: Runs the `build` script and serves it on port 3005
- `build`: Runs the production build
- `test:watch`: Runs tests in watch mode
- `test:unit`: Runs unit tests
- `test`: Runs unit tests with coverage report
- `lint`: Runs linter
- `generate-translations`: Delete all translation files and load new translations
- `generate-translations-dev`: Reload the translations from the service and update / append them to existing files

## Release

General information can be found in [Release Process](https://confluence.dach041.dachser.com/display/10172/Release+Process).
How to release a frontend module can be found in [Frontend Module](https://confluence.dach041.dachser.com/display/10172/Frontend+Module?src=contextnavpagetreemode).

## Microfrontend

DFE is based on a microfrontend architecture with one host app and many remote apps. For more information, see [Confluence](https://confluence.dach041.dachser.com/display/10172/5.+Building+Block+View#id-5.BuildingBlockView-Microfrontends).

### During development

During development, module federation is not used, so apps can be developed as standalone single page applications. To split the individual apps into pages, Vite's is used. Additional pages can be created under `./src/dev/`. (For example, a page called `main/index.html` could be viewed at `/main/`)

### Module federation config

The configuration of the Module Federation plugin is in `vite.config.js`. It expects a `name`, `filename`, which entries are published and which dependencies should be shared.

- `name`: must not contain special characters and will be used in the host app during import.
- `filename`: by convention always "remoteEntry.js".
- `exposes`: key-value pair. The key here is the name (or alias) used in the host app during import. The value is the path to the entry file.
- `shared`: dependencies that should be shared by all apps. Basically, all dependencies can be specified here. Make sure to specify a `requiredVersion`.

```javascript
federation({
  name: "dfeHelloWorldFrontend,
  filename: "remoteEntry.js",
  exposes: {
    "./main": "./src/bootstrap-main",
  },
  shared: package.dependencies,
});
```

Each entry represents an independent app that can be imported.

```javascript
import MainApp from "dfeHelloWorldFrontend/main";
```

### Mount function

For each entry, the host app expects a `mount` function to be able to access. This will render the remote app inside the host. As passing parameters there must be an HTML element and the client.

```javascript
// inside host app
import { mount } from "dfeHelloWorldFrontend/main";

mount(el, client);
```

Inside the `mount` function, a new Vue instance is created that renders a component. This is mounted into the passed HTML element.

```javascript
// inside remote app entry
import MainApp from "@/apps/MainApp.vue";

export const mount = async (el, client) => {
  new Vue({
    render: (h) => h(MainApp),
  }).$mount(el);
};
```

## Styles

The styles are based on the package [dfe-frontend-styles](https://bitbucket.dach041.dachser.com/projects/DFE/repos/dfe-frontend-styles/browse), which provides general, uniform values for DFE such as colors, typography, spacing, etc.

```scss
@use "@dfe/dfe-frontend-styles/build/scss/variables" as vars;

h1 {
  font-size: vars.$font-size-heading-1;
}
```

Vuetify is used with the theme option turned off. It is customized using SCSS within `variables.scss`. It's important that only variables may be defined in this file and any imported files! Further overwriting of Vuetify styles takes place within `overrides.scss`.

Styles for components are created directly in a scoped styles block of Vue files.

### Vuetify scoped CSS

To prevent styles from different Vuetify apps from overwriting each other, a prefix selector is prepended to the CSS in the Vite build process. The script for this is located in `vite.config.js`.

The prefix must match the selector in the root element of the app.

```html
<div dfe-hello-world-frontend>
  <v-app></v-app>
</div>
```

```javascript
prefixer({
  prefix: `[dfe-hello-world-frontend]`,
});
```

The CSS then looks like this:

```css
[dfe-hello-world-frontend] .v-application {
}
```

## I18n

Internationalization is done with the package [vue-i18n](https://github.com/kazupon/vue-i18n). All textual content must be implemented with i18n. The message files are stored under `public/locales`.

## API contract

An API contract is the documentation that describes how the API works and how it will be used. [The documentation exists in the form of a yaml file](https://dfe-hello-world-api-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com/). With the help of the documentation, types and services can be generated automatically. It also forms the basis to implement a mock server for early development as well as for unit testing.

### Generated types and service

By running the npm script `generate-api:hello-world`, the file `generated-hello-world-api.ts` is created based on the yaml documentation. This files contains all types that are defined in the documentation. These can be imported and used in other files.

In addition, an API HTTP client is generated that contains all methods that are defined in the documentation. This client is initialized once with a base URL and a function to retrieve the user token, and can thus be used anywhere in the application to make API calls.

```javascript
const api = createApi({
  baseUrl: "http://localhost",
  getToken: getTokenFn,
});
```

## Client library

The [client library](https://bitbucket.dach041.dachser.com/projects/DFE/repos/dfe-frontend-client/browse) is used to access DFE-wide methods. These include auth, events, logging and more. An instance of the client is initialized inside the host and passed to all remote apps. This can be used, for example, to query the user token, respond to events from other apps, or post to the logging web service.

In Vue, the client instance is inherited from the parent component to all other components using provide/inject.

```javascript
const client = inject(ClientKey);

client?.events.on("createQuote", (payload) => {
  console.log(payload);
});
```

## Testing

Test files are created in the `tests` folder. To run the tests there are the npm scripts `test:watch` (during development) and `test` (to get coverage report).

The threshold for test coverage in SonarQube is currently set to 80%. This includes both Typescript and Vue files.

## SVG icons

### DFE icons

The main source for icons is [dfe-frontend-styles](https://bitbucket.dach041.dachser.com/projects/DFE/repos/dfe-frontend-styles/browse). There are optimized icons in the sizes 16px and 24px. It is important to use the correct size when importing.

```javascript
import DeleteIcon from "@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg";
import AddIcon from "@dfe/dfe-frontend-styles/assets/icons/add-16px.svg";
```

```html
<material-symbol size="24">
  <delete-icon />
</material-symbol>

<material-symbol size="16">
  <add-icon />
</material-symbol>
```

### SVGs as Vue components

By default, SVGs will be loaded as Vue components.

```javascript
<template>
  <home-icon />
</template>

<script>
import HomeIcon from '@/assets/home.svg';

export default {
  components: {
    HomeIcon,
  },
};
</script>
```

### SVGs inline

If you want to disable this plugin for a specific file, add `?raw` when you import. You will receive the raw HTML markup of the SVG which you can, for example, encode as Base64.

Input:

```javascript
import HomeIcon from "@/assets/home.svg?raw";
```

Output:

```html
<svg>...</svg>
```
