import VueI18nPlugin from "@intlify/unplugin-vue-i18n/vite";
import federation from "@originjs/vite-plugin-federation";
import vue from "@vitejs/plugin-vue";
import autoprefixer from "autoprefixer";
import camelCase from "lodash/camelCase";
import { URL, fileURLToPath } from "node:url";
import prefixer from "postcss-prefix-selector";
import { defineConfig } from "vite";
import cssInjectedByJsPlugin from "vite-plugin-css-injected-by-js";
import vuetify, { transformAssetUrls } from "vite-plugin-vuetify";
import svgLoader from "vite-svg-loader";
import path from "path";
import { dependencies, name } from "./package.json";

const shared = Object.entries(dependencies).reduce((shared, [name, version]) => {
  shared[name] = { requiredVersion: version };
  return shared;
}, {});
delete shared.vue;
delete shared.vuetify;
delete dependencies["@tanstack/vue-query"];

export default defineConfig(({ command, mode }) => {
  const buildProd = command === "build" && mode === "production";
  const buildDev = command === "build" && mode === "development";
  const root = path.join(__dirname, "src/dev/");

  return {
    root,
    build: {
      target: "es2022",
      emptyOutDir: true,
      outDir: path.resolve(__dirname, "dist"),
      assetsDir: "",
      rollupOptions: {
        ...(buildDev
          ? {
              input: {
                main: path.resolve(root, "index.html"),
              },
            }
          : {}),
      },
    },
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
      extensions: [".js", ".json", ".jsx", ".mjs", ".ts", ".tsx", ".vue"],
    },
    plugins: [
      vue({
        template: { transformAssetUrls },
        script: {
          defineModel: true,
        },
      }),
      vuetify({
        autoImport: true,
        styles: {
          configFile: path.resolve(__dirname, "src/styles/settings.scss"),
        },
      }),
      VueI18nPlugin({}),
      svgLoader({
        defaultImport: "component",
        svgo: true,
        svgoConfig: {
          plugins: [
            {
              name: "addClassesToSVGElement",
              params: {
                classNames: ["icon"],
              },
            },
          ],
        },
      }),
      ...(buildProd
        ? [
            cssInjectedByJsPlugin({
              jsAssetsFilterFunction: (outputChunk) => {
                return outputChunk.fileName == "remoteEntry.js";
              },
            }),
            federation({
              name: camelCase(name),
              filename: "remoteEntry.js",
              exposes: {
                "./main": "./src/bootstrap-main",
              },
              shared,
            }),
          ]
        : []),
    ],
    ...(command === "serve" && {
      optimizeDeps: {
        exclude: ["vuetify"],
      },
    }),
    define: { "process.env": {} },
    css: {
      postcss: {
        plugins: [
          ...(buildProd
            ? [
                prefixer({
                  prefix: `[${name}]`,
                  transform(prefix, selector, prefixedSelector) {
                    if (selector.startsWith("html") || selector.startsWith("body")) {
                      return prefix + selector.substring(4);
                    }
                    if (selector.startsWith(prefix) || selector.startsWith(":deep")) {
                      return selector;
                    }
                    return prefixedSelector;
                  },
                }),
              ]
            : []),
          autoprefixer({}),
        ],
      },
    },
  };
});
