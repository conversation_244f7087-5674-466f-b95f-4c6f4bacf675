import { config } from "@vue/test-utils";
import { createTesting<PERSON>inia } from "@pinia/testing";
import { i18n } from "@/plugins/i18n";
import vuetify from "@/plugins/vuetify";
import { Api, Api<PERSON><PERSON>, createApi } from "@/services/api";
import { ClientKey } from "@/types/client";
import type { Events } from "@/types/events";
import { createClient } from "@dfe/dfe-frontend-client";
import { VueQueryPlugin } from "@tanstack/vue-query";
import ResizeObserver from "resize-observer-polyfill";

global.ResizeObserver = ResizeObserver;

const client = createClient<Events>({});
const api: Api = {
  helloWorld: createApi("HelloWorld", {
    baseUrl: client.api.getUrl("dfe-hello-world"),
    getToken: () => Promise.resolve("token"),
    getLanguage: () => i18n.global.locale.value,
  }),
};

createTestingPinia({
  createSpy: vi.fn(),
});

i18n.global.missingWarn = false;

config.global.plugins = [vuetify, i18n, VueQueryPlugin];
config.global.provide = {
  [Client<PERSON>ey as symbol]: client,
  [ApiKey as symbol]: api,
};
