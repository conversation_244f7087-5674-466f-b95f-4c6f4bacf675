import { VueWrapper, mount } from "@vue/test-utils";
import MainApp from "@/apps/Main.vue";
import MainView from "@/views/MainView.vue";

describe("Main app", () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    wrapper = mount(MainApp);
  });

  it("sets the project name to the root element", () => {
    expect(wrapper.attributes("dfe-hello-world-frontend")).toBeDefined();
  });

  it("mounts the main view", () => {
    expect(wrapper.findComponent(MainView).exists()).toBe(true);
  });
});
