import MainApp from "@/apps/Main.vue";
import MainView from "@/views/MainView.vue";
import { VueWrapper, mount } from "@vue/test-utils";
import { h } from "vue";

describe("MainView", () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    const parent = mount(MainApp, {
      slots: {
        default: h(MainView),
      },
    });
    wrapper = parent.findComponent(MainView);
  });

  it('renders the "hello world" button', () => {
    expect(wrapper.find("[data-test='hello-world-button']").exists()).toBe(true);
  });
});
