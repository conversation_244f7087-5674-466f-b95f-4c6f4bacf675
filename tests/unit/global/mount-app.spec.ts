import { mountApp } from "@/mount-app";
import { Events } from "@/types/events";
import { createClient } from "@dfe/dfe-frontend-client";
import MainApp from "@/apps/Main.vue";

describe("mount-app", () => {
  const client = createClient<Events>({});
  let mounterFn: ReturnType<typeof mountApp>;
  let mountTarget: HTMLDivElement;

  vi.spyOn(client.auth, "getToken").mockImplementation(async () => "token");

  beforeAll(() => {
    mountTarget = document.createElement("div");
    document.body.appendChild(mountTarget);
  });

  beforeEach(() => {
    mounterFn = mountApp(MainApp);
  });

  it("returns a mounting function for a given app", () => {
    expect(mounterFn).toBeInstanceOf(Function);
  });

  it("mounts onto HTMLElement", async () => {
    await mounterFn(mountTarget, client);
    expect(document.querySelector("[dfe-hello-world-frontend]")).toBeTruthy();
  });
});
