import { describe, expect, it, vi } from "vitest";
import createFetchMock from "vitest-fetch-mock";

import { createApi } from "@/services/api";

const fetchMocker = createFetchMock(vi);
fetchMocker.enableMocks();

const params = {
  baseUrl: "http://localhost",
  path: "/api",
  secure: true,
};
const token = "token123";
const language = "en";

describe("api service", () => {
  const api = createApi("HelloWorld", {
    getToken: () => new Promise((resolve) => resolve(token)),
    getLanguage: () => language,
  });

  it("adds an authorization and language header to requests", async () => {
    await api.request(params);

    expect(window.fetch).toHaveBeenCalledWith(
      "http://localhost/api",
      expect.objectContaining({
        headers: expect.objectContaining({
          Authorization: `Bearer ${token}`,
          "Accept-Language": language,
        }),
      })
    );
  });
});
