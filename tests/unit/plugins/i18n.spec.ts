import { i18n, useLocale } from "@/plugins/i18n";

describe("i18n plugin", () => {
  it("uses default locale", () => {
    expect(i18n.global.locale.value).toEqual("en");
  });

  it("loads and sets locale", async () => {
    await useLocale("de");

    expect(i18n.global.locale.value).toEqual("de");
    // @ts-expect-error - "de" is not defined in the type
    expect(i18n.global.messages.value.de).toBeDefined();
  });

  it("sets locale when it is already in use", async () => {
    await useLocale("de");
    expect(i18n.global.locale.value).toEqual("de");
  });

  it("sets locale that is already loaded", async () => {
    await useLocale("en");
    expect(i18n.global.locale.value).toEqual("en");
  });
});
