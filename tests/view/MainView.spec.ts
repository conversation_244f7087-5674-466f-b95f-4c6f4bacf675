import MainView from "@/views/MainView.vue";
import { mount, VueWrapper } from "@vue/test-utils";
import { ApiKey, createApi } from "@/services/api";

const api = createApi("HelloWorld", {
  getLanguage: vi.fn(),
  getToken: vi.fn(),
});

const getCustomersSpy = vi.spyOn(api.customers, "getCustomers");
describe("MainView", () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    wrapper = mount(MainView, {
      global: {
        provide: {
          [ApiKey as symbol]: { helloWorld: api },
        },
      },
    });
  });
  it("fetches customers", () => {
    expect(getCustomersSpy).toHaveBeenCalledTimes(1);
  });

  it("renders a button", () => {
    const button = wrapper.findComponent({ name: "v-btn" });
    expect(button.exists()).toBe(true);
  });
});
